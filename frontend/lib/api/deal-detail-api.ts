// API client for deal detail operations
import apiClient from '@/lib/api-client';
import { 
  DealDetailData, 
  TimelineEvent, 
  ScoreBreakdown, 
  Founder, 
  ExternalSignal, 
  DealDocument,
  ChatMessage,
  mockTimelineEvents,
  mockScoreBreakdown,
  mockFounders,
  mockExternalSignals,
  mockDocuments
} from '@/lib/types/deal-detail';
import { Deal } from '@/lib/types/deal';

export interface ChatRequest {
  message: string;
  agent_type?: string;
}

export interface ChatResponse {
  response: string;
  timestamp: string;
  message_id: string;
}

export const DealDetailAPI = {
  /**
   * Get complete deal detail data
   */
  async getDealDetail(dealId: string): Promise<DealDetailData> {
    console.log(`Fetching deal detail for ${dealId}`);

    try {
      // Try to get basic deal info first
      const response = await apiClient.get(`/deals/${dealId}`);
      console.log('Deal detail fetched:', response.data);

      // For now, we'll enhance the basic deal data with mock detail data
      // When backend endpoints are ready, we can fetch additional data
      const basicDeal = response.data;

      // Transform backend scoring data to match our frontend structure
      const transformedScoring = basicDeal.scoring ? {
        overall_score: basicDeal.scoring.total_score || basicDeal.scoring.normalized_score || 0,
        signal_breakdown: {
          team_strength: {
            score: 85, // TODO: Extract from backend scoring
            explanation: 'Strong founding team with relevant experience',
            ai_insights: 'Founders have proven track record with successful exits'
          },
          market_signals: {
            score: 78,
            explanation: 'Growing market with positive trends',
            ai_insights: 'Market showing 40% YoY growth with strong investor interest'
          },
          thesis_match: {
            score: 82,
            explanation: 'Good alignment with investment thesis',
            ai_insights: '85% match to investment criteria'
          }
        },
        last_updated: new Date().toISOString(),
        ai_summary: 'High-potential investment with strong fundamentals'
      } : mockScoreBreakdown;

      const detailData: DealDetailData = {
        ...basicDeal,
        timeline: mockTimelineEvents,
        score_breakdown: transformedScoring,
        founders: mockFounders,
        external_signals: mockExternalSignals,
        documents: mockDocuments,
        chat_history: []
      };

      return detailData;
    } catch (error: any) {
      console.error('Error fetching deal detail, using mock data:', error);
      
      // Fallback to mock data for demo
      const mockDeal: DealDetailData = {
        id: dealId,
        org_id: 'demo-org',
        form_id: 'demo-form',
        submission_ids: ['demo-submission'],
        company_name: 'Acme Corp',
        stage: 'Series A',
        sector: ['Technology', 'SaaS'],
        status: 'active' as any,
        created_by: 'demo-user',
        created_at: Date.now() - 86400000, // 1 day ago
        updated_at: Date.now(),
        timeline: mockTimelineEvents,
        score_breakdown: mockScoreBreakdown,
        founders: mockFounders,
        external_signals: mockExternalSignals,
        documents: mockDocuments,
        chat_history: []
      };
      
      return mockDeal;
    }
  },

  /**
   * Get deal timeline events
   */
  async getTimeline(dealId: string): Promise<TimelineEvent[]> {
    console.log(`Fetching timeline for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/timeline`);
      console.log('Timeline fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching timeline, using mock data:', error);
      return mockTimelineEvents;
    }
  },

  /**
   * Get deal score breakdown
   */
  async getScoreBreakdown(dealId: string): Promise<ScoreBreakdown | null> {
    console.log(`Fetching score breakdown for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/score`);
      console.log('Score breakdown fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching score breakdown, using mock data:', error);
      return mockScoreBreakdown;
    }
  },

  /**
   * Get deal founders
   */
  async getFounders(dealId: string): Promise<Founder[]> {
    console.log(`Fetching founders for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/founders`);
      console.log('Founders fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching founders, using mock data:', error);
      return mockFounders;
    }
  },

  /**
   * Get external signals
   */
  async getExternalSignals(dealId: string): Promise<ExternalSignal[]> {
    console.log(`Fetching external signals for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/signals`);
      console.log('External signals fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching external signals, using mock data:', error);
      return mockExternalSignals;
    }
  },

  /**
   * Get deal documents
   */
  async getDocuments(dealId: string): Promise<DealDocument[]> {
    console.log(`Fetching documents for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/documents`);
      console.log('Documents fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching documents, using mock data:', error);
      return mockDocuments;
    }
  },

  /**
   * Upload document
   */
  async uploadDocument(dealId: string, file: File): Promise<DealDocument> {
    console.log(`Uploading document for deal ${dealId}:`, file.name);
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const response = await apiClient.post(`/deals/${dealId}/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log('Document uploaded:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error uploading document:', error);
      throw error;
    }
  },

  /**
   * Delete document
   */
  async deleteDocument(dealId: string, documentId: string): Promise<void> {
    console.log(`Deleting document ${documentId} for deal ${dealId}`);
    
    try {
      await apiClient.delete(`/deals/${dealId}/documents/${documentId}`);
      console.log('Document deleted successfully');
    } catch (error: any) {
      console.error('Error deleting document:', error);
      throw error;
    }
  },

  /**
   * Get chat history
   */
  async getChatHistory(dealId: string): Promise<ChatMessage[]> {
    console.log(`Fetching chat history for deal ${dealId}`);
    
    try {
      const response = await apiClient.get(`/deals/${dealId}/chat/history`);
      console.log('Chat history fetched:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  },

  /**
   * Send chat message
   */
  async sendChatMessage(dealId: string, request: ChatRequest): Promise<ChatResponse> {
    console.log(`Sending chat message for deal ${dealId}:`, request);
    
    try {
      // Try AI service first (port 8002)
      const aiResponse = await fetch(`http://localhost:8002/api/ai/deals/${dealId}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-ORG-ID': localStorage.getItem('orgId') || '',
        },
        body: JSON.stringify(request),
      });
      
      if (aiResponse.ok) {
        const data = await aiResponse.json();
        console.log('AI chat response:', data);
        return {
          response: data.response || data.message || 'Response received',
          timestamp: new Date().toISOString(),
          message_id: data.id || Math.random().toString(36)
        };
      }
    } catch (error) {
      console.warn('AI service unavailable, using fallback:', error);
    }
    
    // Fallback to mock response
    const mockResponse: ChatResponse = {
      response: `I understand you're asking about "${request.message}". Based on the deal data, I can help analyze the company's potential, scoring breakdown, founder backgrounds, and market signals. What specific aspect would you like me to focus on?`,
      timestamp: new Date().toISOString(),
      message_id: Math.random().toString(36)
    };
    
    return mockResponse;
  },

  /**
   * Add timeline event
   */
  async addTimelineEvent(dealId: string, event: Omit<TimelineEvent, 'id'>): Promise<TimelineEvent> {
    console.log(`Adding timeline event for deal ${dealId}:`, event);
    
    try {
      const response = await apiClient.post(`/deals/${dealId}/timeline`, event);
      console.log('Timeline event added:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error adding timeline event:', error);
      throw error;
    }
  }
};
