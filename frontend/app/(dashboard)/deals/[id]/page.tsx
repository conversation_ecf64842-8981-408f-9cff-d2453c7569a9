"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Shell } from "@/components/shell"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { DealDetailData } from "@/lib/types/deal-detail"
import { useAuth } from "@/lib/auth-context"
import { DealHeader } from "@/components/core/deals/deal-detail/deal-header"
import { DealTabs } from "@/components/core/deals/deal-detail/deal-tabs"
import { AiChat } from "@/components/core/deals/deal-detail/ai-chat"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export default function DealDetailPage() {
  const params = useParams()
  const dealId = params?.id as string
  const { isAuthenticated } = useAuth()
  
  const [dealData, setDealData] = useState<DealDetailData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('timeline')

  useEffect(() => {
    if (!isAuthenticated || !dealId) return

    const fetchDealDetail = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const data = await DealDetailAPI.getDealDetail(dealId)
        setDealData(data)
      } catch (err: any) {
        console.error('Error fetching deal detail:', err)
        setError('Failed to load deal details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchDealDetail()
  }, [isAuthenticated, dealId])

  // Loading state
  if (loading) {
    return (
      <Shell className="max-w-none">
        <div className="space-y-8">
          {/* Header skeleton */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-32" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-24" />
              </div>
            </div>
          </div>
          
          {/* Tabs skeleton */}
          <div className="space-y-6">
            <div className="flex space-x-8 border-b">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-10 w-20" />
              ))}
            </div>
            
            {/* Content skeleton */}
            <div className="space-y-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          </div>
        </div>
      </Shell>
    )
  }

  // Error state
  if (error) {
    return (
      <Shell className="max-w-none">
        <Alert variant="destructive" className="max-w-md mx-auto mt-8">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      </Shell>
    )
  }

  // No data state
  if (!dealData) {
    return (
      <Shell className="max-w-none">
        <Alert className="max-w-md mx-auto mt-8">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Deal not found.
          </AlertDescription>
        </Alert>
      </Shell>
    )
  }

  return (
    <Shell className="max-w-none px-4 lg:px-8">
      <div className="space-y-8">
        {/* Deal Header - Investor-First Design */}
        <DealHeader deal={dealData} />

        {/* Main Content Area - Premium Layout */}
        <div className="flex gap-8">
          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <DealTabs
              deal={dealData}
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />
          </div>

          {/* AI Assistant Panel - Investor's Superpower */}
          <div className="hidden xl:block w-96 flex-shrink-0">
            <div className="sticky top-8">
              <AiChat dealId={dealId} />
            </div>
          </div>
        </div>

        {/* Mobile AI Chat - Full-width modal */}
        <div className="xl:hidden">
          <AiChat dealId={dealId} isMobile />
        </div>
      </div>
    </Shell>
  )
}
