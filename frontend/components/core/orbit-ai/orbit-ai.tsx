"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Send,
  Minus,
  GripVertical,
  Loader2,
  HelpCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { OrbitIcon } from "./orbit-icon"


interface Message {
  id: string
  content: string
  sender: 'user' | 'orbit'
  timestamp: Date
}

interface OrbitAIProps {
  dealId?: string
  dealContext?: any
  className?: string
}

const getContextualPrompts = (dealContext: any) => {
  const basePrompts = [
    "Summarize this deal",
    "What are the key risks?",
    "How does this score compare?"
  ]

  if (dealContext?.score_breakdown) {
    return [
      `Explain the ${dealContext.score_breakdown.overall_score} score`,
      "What drives the team strength rating?",
      "Analyze market signals"
    ]
  }

  return basePrompts
}

export function OrbitAI({ dealId, dealContext, className }: OrbitAIProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)
  const [activeMode, setActiveMode] = useState<'chat' | 'agent'>('chat')
  const dragRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Initialize position (bottom right)
  useEffect(() => {
    const updatePosition = () => {
      const savedPosition = sessionStorage.getItem('orbit-position')
      if (savedPosition) {
        setPosition(JSON.parse(savedPosition))
      } else {
        // Default to bottom right
        setPosition({
          x: window.innerWidth - (isExpanded ? 380 : 200),
          y: window.innerHeight - (isExpanded ? 620 : 80)
        })
      }
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    return () => window.removeEventListener('resize', updatePosition)
  }, [isExpanded])

  // Show tooltip on first open
  useEffect(() => {
    if (isExpanded && messages.length === 0) {
      setShowTooltip(true)
      const timer = setTimeout(() => setShowTooltip(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [isExpanded, messages.length])

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Drag functionality
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      
      const newX = Math.max(0, Math.min(window.innerWidth - (isExpanded ? 360 : 200), e.clientX - 100))
      const newY = Math.max(0, Math.min(window.innerHeight - (isExpanded ? 600 : 60), e.clientY - 30))
      
      setPosition({ x: newX, y: newY })
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        sessionStorage.setItem('orbit-position', JSON.stringify(position))
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, position, isExpanded])

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    try {
      // TODO: Replace with actual AI API call
      // Simulate context-aware AI response
      await new Promise(resolve => setTimeout(resolve, 1500))

      let aiResponse = `I understand you're asking about "${content}".`

      // Context-aware responses
      if (dealContext) {
        const companyName = dealContext.company_name || "this company"
        const score = dealContext.score_breakdown?.overall_score

        if (content.toLowerCase().includes('score')) {
          aiResponse = `${companyName} has an overall score of ${score}/100. This reflects strong performance across our key investment criteria.`
        } else if (content.toLowerCase().includes('risk')) {
          aiResponse = `Based on my analysis of ${companyName}, the key risks include market competition and execution challenges. However, the strong founding team mitigates many concerns.`
        } else if (content.toLowerCase().includes('team')) {
          aiResponse = `The founding team at ${companyName} shows exceptional strength with proven track records and relevant domain expertise.`
        } else {
          aiResponse = `Based on the deal data for ${companyName}, here's my analysis: This is a ${score >= 80 ? 'high-potential' : score >= 60 ? 'promising' : 'developing'} investment opportunity.`
        }
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        sender: 'orbit',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Connection Error",
        description: "Unable to reach Orbit AI. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsTyping(false)
    }
  }

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  return (
    <motion.div
        ref={dragRef}
        className={cn("fixed z-50 select-none", className)}
        style={{
          left: position.x,
          top: position.y,
        }}
        animate={{
          scale: isDragging ? 1.05 : 1,
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <AnimatePresence mode="wait">
          {!isExpanded ? (
            // Collapsed State - Futuristic Chat Bubble
            <motion.div
              key="collapsed"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className={cn(
                "group cursor-pointer",
                "backdrop-blur-xl bg-white/70 dark:bg-black/70",
                "border-2 border-primary/50",
                "rounded-2xl shadow-2xl",
                "hover:shadow-primary/30 hover:shadow-2xl hover:border-primary/80",
                "transition-all duration-300 ease-out",
                "hover:bg-white/80 dark:hover:bg-black/80",
                isDragging && "shadow-primary/40 shadow-2xl"
              )}
              onClick={() => setIsExpanded(true)}
              onMouseDown={() => setIsDragging(true)}
              whileHover={{
                scale: 1.05,
                boxShadow: "0 0 20px rgba(56, 189, 248, 0.3)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="flex items-center gap-3 px-4 py-3">
                <OrbitIcon
                  animated={true}
                  glowing={true}
                  className="h-5 w-5"
                />
                <span className="font-semibold text-foreground font-['Space_Grotesk',sans-serif]">
                  Ask Orbit
                </span>
              </div>
            </motion.div>
        ) : (
          // Expanded State - Futuristic Chat Panel
          <motion.div
            key="expanded"
            initial={{
              opacity: 0,
              scale: 0.8,
              x: 0,
              y: 0
            }}
            animate={{
              opacity: 1,
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{
              opacity: 0,
              scale: 0.8,
              x: 0,
              y: 0
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 25,
              duration: 0.4
            }}
            className={cn(
              "w-[360px] h-[600px] max-h-[80vh]",
              "backdrop-blur-2xl bg-white/70 dark:bg-black/70",
              "border-2 border-primary/50",
              "rounded-2xl shadow-2xl",
              "flex flex-col overflow-hidden",
              "font-['Space_Grotesk',sans-serif]",
              isDragging && "shadow-primary/40 shadow-2xl"
            )}
            style={{
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 30px rgba(56, 189, 248, 0.1)"
            }}
          >
            {/* Futuristic Header with Mode Toggle */}
            <div
              className={cn(
                "flex items-center justify-between p-4",
                "border-b border-primary/20",
                "cursor-grab active:cursor-grabbing",
                "bg-gradient-to-r from-transparent via-primary/5 to-transparent",
                isDragging && "cursor-grabbing"
              )}
              onMouseDown={() => setIsDragging(true)}
            >
              <div className="flex items-center gap-3">
                <OrbitIcon
                  animated={true}
                  glowing={false}
                  className="h-5 w-5"
                />
                <span className="font-semibold text-foreground text-lg">Orbit AI</span>
                <Badge
                  variant="secondary"
                  className="text-xs bg-primary/10 text-primary border-primary/20 backdrop-blur-xl"
                >
                  Powered by AI
                </Badge>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center gap-3">
                <div className="flex items-center bg-white/10 dark:bg-black/10 rounded-full p-1 backdrop-blur-xl border border-primary/20">
                  <button
                    className={cn(
                      "px-3 py-1 text-xs font-medium rounded-full transition-all duration-200",
                      activeMode === 'chat'
                        ? "bg-primary text-primary-foreground shadow-lg"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                    onClick={() => setActiveMode('chat')}
                  >
                    Chat
                  </button>
                  <button
                    className={cn(
                      "px-3 py-1 text-xs font-medium rounded-full transition-all duration-200 opacity-50 cursor-not-allowed",
                      "text-muted-foreground"
                    )}
                    disabled
                    title="Agent Mode – Coming Soon"
                  >
                    Agent
                  </button>
                </div>

                <div className="flex items-center gap-1">
                  <GripVertical className="h-4 w-4 text-muted-foreground/50" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="h-8 w-8 p-0 hover:bg-primary/10 rounded-full transition-all duration-200"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Futuristic Chat Area */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <OrbitIcon
                      animated={true}
                      glowing={true}
                      className="h-12 w-12 mx-auto mb-4"
                    />
                    <p className="text-base font-medium text-foreground mb-2">
                      Hi! I'm Orbit, your AI investment assistant.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Ask me anything about this deal.
                    </p>
                  </div>
                )}

                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                    className={cn(
                      "flex",
                      message.sender === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] px-4 py-3 rounded-2xl",
                        "backdrop-blur-xl border",
                        message.sender === 'user'
                          ? "bg-primary/20 border-primary/30 text-foreground ml-4 shadow-lg shadow-primary/10"
                          : "bg-white/40 dark:bg-black/40 border-white/20 dark:border-black/20 text-foreground mr-4"
                      )}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                    </div>
                  </motion.div>
                ))}

                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex justify-start"
                  >
                    <div className="bg-white/40 dark:bg-black/40 backdrop-blur-xl px-4 py-3 rounded-2xl mr-4 border border-white/20 dark:border-black/20">
                      <div className="flex items-center gap-1">
                        <motion.div
                          className="w-2 h-2 bg-primary rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                        />
                        <motion.div
                          className="w-2 h-2 bg-primary rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                        />
                        <motion.div
                          className="w-2 h-2 bg-primary rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Futuristic Quick Prompts */}
            {messages.length === 0 && (
              <div className="px-4 pb-3">
                <div className="flex flex-wrap gap-2">
                  {getContextualPrompts(dealContext).map((prompt, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleQuickPrompt(prompt)}
                        className={cn(
                          "text-xs font-medium",
                          "bg-white/20 dark:bg-black/20 backdrop-blur-xl",
                          "hover:bg-primary/20 hover:border-primary/40",
                          "border border-white/20 dark:border-black/20",
                          "rounded-full transition-all duration-300",
                          "hover:scale-105 hover:shadow-lg hover:shadow-primary/10"
                        )}
                      >
                        {prompt}
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Futuristic Input Bar */}
            <div className="p-4 border-t border-primary/20 bg-gradient-to-r from-transparent via-primary/5 to-transparent">
              <div className="flex gap-3">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask Orbit..."
                  className={cn(
                    "flex-1 bg-white/30 dark:bg-black/30 backdrop-blur-xl",
                    "border-white/30 dark:border-black/30",
                    "rounded-2xl px-4 py-3",
                    "placeholder:text-muted-foreground/70",
                    "focus:border-primary/50 focus:ring-2 focus:ring-primary/20",
                    "transition-all duration-300"
                  )}
                  disabled={isTyping}
                />
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => handleSendMessage(inputValue)}
                    disabled={!inputValue.trim() || isTyping}
                    className={cn(
                      "px-4 py-3 rounded-2xl",
                      "bg-primary hover:bg-primary/90",
                      "shadow-lg transition-all duration-300",
                      inputValue.trim() && "shadow-primary/30 shadow-xl"
                    )}
                  >
                    {isTyping ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </motion.div>
              </div>

              {/* Futuristic Tooltip */}
              <AnimatePresence>
                {showTooltip && (
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.9 }}
                    className="text-center mt-3"
                  >
                    <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 border border-primary/20 backdrop-blur-xl">
                      <OrbitIcon className="h-3 w-3" />
                      <p className="text-xs text-primary font-medium">
                        Drag anywhere to reposition
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
        </AnimatePresence>
      </motion.div>
  )
}
