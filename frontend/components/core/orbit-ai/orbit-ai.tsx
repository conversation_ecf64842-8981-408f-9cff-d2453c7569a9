"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Wand2, 
  Send, 
  Minus, 
  GripVertical,
  Loader2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"

interface Message {
  id: string
  content: string
  sender: 'user' | 'orbit'
  timestamp: Date
}

interface OrbitAIProps {
  dealId?: string
  dealContext?: any
  className?: string
}

const getContextualPrompts = (dealContext: any) => {
  const basePrompts = [
    "Summarize this deal",
    "What are the key risks?",
    "How does this score compare?"
  ]

  if (dealContext?.score_breakdown) {
    return [
      `Explain the ${dealContext.score_breakdown.overall_score} score`,
      "What drives the team strength rating?",
      "Analyze market signals"
    ]
  }

  return basePrompts
}

export function OrbitAI({ dealId, dealContext, className }: OrbitAIProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)
  const dragRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  // Initialize position (bottom right)
  useEffect(() => {
    const updatePosition = () => {
      const savedPosition = sessionStorage.getItem('orbit-position')
      if (savedPosition) {
        setPosition(JSON.parse(savedPosition))
      } else {
        // Default to bottom right
        setPosition({
          x: window.innerWidth - (isExpanded ? 380 : 200),
          y: window.innerHeight - (isExpanded ? 620 : 80)
        })
      }
    }

    updatePosition()
    window.addEventListener('resize', updatePosition)
    return () => window.removeEventListener('resize', updatePosition)
  }, [isExpanded])

  // Show tooltip on first open
  useEffect(() => {
    if (isExpanded && messages.length === 0) {
      setShowTooltip(true)
      const timer = setTimeout(() => setShowTooltip(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [isExpanded, messages.length])

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Drag functionality
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return
      
      const newX = Math.max(0, Math.min(window.innerWidth - (isExpanded ? 360 : 200), e.clientX - 100))
      const newY = Math.max(0, Math.min(window.innerHeight - (isExpanded ? 600 : 60), e.clientY - 30))
      
      setPosition({ x: newX, y: newY })
    }

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false)
        sessionStorage.setItem('orbit-position', JSON.stringify(position))
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, position, isExpanded])

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue("")
    setIsTyping(true)

    try {
      // TODO: Replace with actual AI API call
      // Simulate context-aware AI response
      await new Promise(resolve => setTimeout(resolve, 1500))

      let aiResponse = `I understand you're asking about "${content}".`

      // Context-aware responses
      if (dealContext) {
        const companyName = dealContext.company_name || "this company"
        const score = dealContext.score_breakdown?.overall_score

        if (content.toLowerCase().includes('score')) {
          aiResponse = `${companyName} has an overall score of ${score}/100. This reflects strong performance across our key investment criteria.`
        } else if (content.toLowerCase().includes('risk')) {
          aiResponse = `Based on my analysis of ${companyName}, the key risks include market competition and execution challenges. However, the strong founding team mitigates many concerns.`
        } else if (content.toLowerCase().includes('team')) {
          aiResponse = `The founding team at ${companyName} shows exceptional strength with proven track records and relevant domain expertise.`
        } else {
          aiResponse = `Based on the deal data for ${companyName}, here's my analysis: This is a ${score >= 80 ? 'high-potential' : score >= 60 ? 'promising' : 'developing'} investment opportunity.`
        }
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        sender: 'orbit',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Connection Error",
        description: "Unable to reach Orbit AI. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsTyping(false)
    }
  }

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  return (
    <motion.div
      ref={dragRef}
      className={cn("fixed z-50 select-none", className)}
      style={{
        left: position.x,
        top: position.y,
      }}
      animate={{
        scale: isDragging ? 1.05 : 1,
      }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <AnimatePresence mode="wait">
        {!isExpanded ? (
          // Collapsed State - Chat Bubble
          <motion.div
            key="collapsed"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className={cn(
              "group cursor-pointer",
              "backdrop-blur-xl bg-white/30 dark:bg-black/30",
              "border-2 border-primary/50",
              "rounded-2xl shadow-2xl",
              "hover:shadow-primary/20 hover:shadow-2xl hover:border-primary/70",
              "transition-all duration-300 ease-out",
              "hover:bg-white/40 dark:hover:bg-black/40",
              isDragging && "shadow-primary/30 shadow-2xl scale-105"
            )}
            onClick={() => setIsExpanded(true)}
            onMouseDown={() => setIsDragging(true)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="flex items-center gap-3 px-4 py-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Wand2 className="h-5 w-5 text-primary" />
              </motion.div>
              <span className="font-semibold text-foreground">Ask Orbit</span>
            </div>
          </motion.div>
        ) : (
          // Expanded State - Chat Panel
          <motion.div
            key="expanded"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className={cn(
              "w-[360px] h-[600px] max-h-[80vh]",
              "backdrop-blur-2xl bg-white/50 dark:bg-black/50",
              "border-2 border-primary/50",
              "rounded-2xl shadow-2xl",
              "flex flex-col overflow-hidden",
              isDragging && "shadow-primary/30 shadow-2xl"
            )}
          >
            {/* Header */}
            <div 
              className={cn(
                "flex items-center justify-between p-4 border-b border-white/20 dark:border-black/20",
                "cursor-grab active:cursor-grabbing",
                isDragging && "cursor-grabbing"
              )}
              onMouseDown={() => setIsDragging(true)}
            >
              <div className="flex items-center gap-3">
                <Wand2 className="h-5 w-5 text-primary" />
                <span className="font-semibold text-foreground">Orbit AI</span>
                <Badge variant="secondary" className="text-xs bg-white/20 dark:bg-black/20">
                  Powered by AI
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <GripVertical className="h-4 w-4 text-muted-foreground" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(false)}
                  className="h-8 w-8 p-0 hover:bg-white/20 dark:hover:bg-black/20"
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Chat Area */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <Wand2 className="h-8 w-8 text-primary mx-auto mb-3" />
                    <p className="text-sm text-muted-foreground">
                      Hi! I'm Orbit, your AI investment assistant.
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Ask me anything about this deal.
                    </p>
                  </div>
                )}

                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={cn(
                      "flex",
                      message.sender === 'user' ? "justify-end" : "justify-start"
                    )}
                  >
                    <div
                      className={cn(
                        "max-w-[80%] px-3 py-2 rounded-2xl",
                        "backdrop-blur-xl",
                        message.sender === 'user'
                          ? "bg-primary/20 text-primary-foreground ml-4"
                          : "bg-white/30 dark:bg-black/30 text-foreground mr-4"
                      )}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                    </div>
                  </motion.div>
                ))}

                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex justify-start"
                  >
                    <div className="bg-white/30 dark:bg-black/30 backdrop-blur-xl px-3 py-2 rounded-2xl mr-4">
                      <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-primary rounded-full animate-bounce" />
                        <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                        <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                      </div>
                    </div>
                  </motion.div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Quick Prompts */}
            {messages.length === 0 && (
              <div className="px-4 pb-2">
                <div className="flex flex-wrap gap-2">
                  {getContextualPrompts(dealContext).map((prompt, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleQuickPrompt(prompt)}
                      className="text-xs bg-white/20 dark:bg-black/20 hover:bg-white/30 dark:hover:bg-black/30 border border-white/20 dark:border-black/20 transition-all duration-200 hover:scale-105"
                    >
                      {prompt}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Input Bar */}
            <div className="p-4 border-t border-white/20 dark:border-black/20">
              <div className="flex gap-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask Orbit..."
                  className="flex-1 bg-white/20 dark:bg-black/20 border-white/20 dark:border-black/20 backdrop-blur-xl"
                  disabled={isTyping}
                />
                <Button
                  onClick={() => handleSendMessage(inputValue)}
                  disabled={!inputValue.trim() || isTyping}
                  className={cn(
                    "px-3",
                    inputValue.trim() && "shadow-lg shadow-primary/20"
                  )}
                >
                  {isTyping ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Tooltip */}
              <AnimatePresence>
                {showTooltip && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="text-center mt-2"
                  >
                    <p className="text-xs text-muted-foreground">
                      💡 Drag anywhere to reposition
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
