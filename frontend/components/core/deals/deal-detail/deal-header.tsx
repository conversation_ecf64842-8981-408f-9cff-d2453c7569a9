"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  ChevronLeft,
  FileText,
  Share2,
  MessageSquare,
  MoreHorizontal,
  Star,
  Flag,
  Sparkles,
  Users,
  FileIcon,
  Radio,
  Clock
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface DealHeaderProps {
  deal: DealDetailData
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-50/50 text-blue-600 border-blue-200/50 before:bg-blue-500'
    case 'active':
      return 'bg-green-50/50 text-green-600 border-green-200/50 before:bg-green-500'
    case 'triage':
      return 'bg-yellow-50/50 text-yellow-600 border-yellow-200/50 before:bg-yellow-500'
    case 'completed':
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50 before:bg-gray-500'
    case 'flagged':
      return 'bg-red-50/50 text-red-600 border-red-200/50 before:bg-red-500'
    case 'hard_pass':
      return 'bg-red-50/50 text-red-600 border-red-200/50 before:bg-red-500'
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50 before:bg-gray-500'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-50/50 text-purple-600 border-purple-200/50'
    case 'seed':
      return 'bg-blue-50/50 text-blue-600 border-blue-200/50'
    case 'series a':
      return 'bg-green-50/50 text-green-600 border-green-200/50'
    case 'series b':
      return 'bg-orange-50/50 text-orange-600 border-orange-200/50'
    case 'series c':
      return 'bg-red-50/50 text-red-600 border-red-200/50'
    default:
      return 'bg-gray-50/50 text-gray-600 border-gray-200/50'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const formatTimeAgo = (timestamp: string | number) => {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export function DealHeader({ deal }: DealHeaderProps) {
  const [isStarred, setIsStarred] = useState(false)

  const handleStarToggle = () => {
    setIsStarred(!isStarred)
    // TODO: Implement star/bookmark functionality
  }

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share deal:', deal.id)
  }

  const handleCreateMemo = () => {
    // TODO: Implement deal memo creation
    console.log('Create memo for deal:', deal.id)
  }

  const overallScore = deal.score_breakdown?.overall_score || 0
  const foundersCount = deal.founders?.length || 0
  const documentsCount = deal.documents?.length || 0
  const signalsCount = deal.external_signals?.length || 0
  const lastUpdated = formatTimeAgo(deal.updated_at)

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-muted-foreground">
        <Link
          href="/deals"
          className="flex items-center hover:text-foreground transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Deals
        </Link>
      </div>

      {/* Above the Fold - Investor Snapshot */}
      <div className="space-y-6">
        {/* Company Name & Star */}
        <div className="flex items-center gap-4">
          <h1 className="text-4xl font-bold tracking-tight text-left">
            {deal.company_name || 'Unnamed Company'}
          </h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleStarToggle}
            className={cn(
              "p-2 h-10 w-10 rounded-full hover:bg-muted/50 transition-colors",
              isStarred ? "text-yellow-500" : "text-muted-foreground"
            )}
          >
            <Star className={cn("h-5 w-5", isStarred && "fill-current")} />
          </Button>
        </div>

        {/* Chips Row */}
        <div className="flex flex-wrap items-center gap-3">
          {/* Status Chip with Dot */}
          <Badge
            variant="outline"
            className={cn(
              "relative pl-6 font-medium border-0 rounded-full",
              getStatusColor(deal.status),
              "before:absolute before:left-2 before:top-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:rounded-full"
            )}
          >
            {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
          </Badge>

          {/* Stage Chip */}
          {deal.stage && (
            <Badge
              variant="outline"
              className={cn("font-medium border-0 rounded-full", getStageColor(deal.stage))}
            >
              {deal.stage}
            </Badge>
          )}

          {/* Sector Chips */}
          {deal.sector && (
            <>
              {Array.isArray(deal.sector) ? (
                <>
                  <Badge variant="secondary" className="rounded-full">
                    {deal.sector[0]}
                  </Badge>
                  {deal.sector.length > 1 && (
                    <Badge variant="secondary" className="rounded-full">
                      {deal.sector.length > 2 ? `+${deal.sector.length - 1} more` : deal.sector[1]}
                    </Badge>
                  )}
                </>
              ) : (
                <Badge variant="secondary" className="rounded-full">
                  {deal.sector}
                </Badge>
              )}
            </>
          )}

          {/* Premium Score Badge */}
          {overallScore > 0 && (
            <div className="relative">
              <div className={cn(
                "flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br shadow-lg",
                getScoreColor(overallScore)
              )}>
                <div className="flex items-center justify-center w-14 h-14 rounded-full bg-white dark:bg-gray-900">
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {overallScore}
                  </span>
                </div>
              </div>
              <Progress
                value={overallScore}
                className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-12 h-1"
              />
            </div>
          )}

          {/* Country Flag - TODO: Add when available */}
        </div>

        {/* Meta Line */}
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          <span>{foundersCount} founder{foundersCount !== 1 ? 's' : ''}</span>
          <span className="mx-2">·</span>
          <FileIcon className="h-4 w-4" />
          <span>{documentsCount} document{documentsCount !== 1 ? 's' : ''}</span>
          <span className="mx-2">·</span>
          <Radio className="h-4 w-4" />
          <span>{signalsCount} signal{signalsCount !== 1 ? 's' : ''}</span>
          <span className="mx-2">·</span>
          <Clock className="h-4 w-4" />
          <span>Last updated {lastUpdated}</span>
        </div>
      </div>

      {/* Actions Row */}
      <div className="flex items-center justify-end gap-3">
        <Button
          variant="outline"
          onClick={handleCreateMemo}
          className="gap-2 rounded-full"
        >
          <Sparkles className="h-4 w-4" />
          Create Memo
          <span className="text-xs text-muted-foreground ml-1">Powered by AI</span>
        </Button>

        <Button
          variant="ghost"
          onClick={handleShare}
          className="gap-2 rounded-full"
        >
          <Share2 className="h-4 w-4" />
          Share
        </Button>

        <Button
          variant="ghost"
          className="gap-2 rounded-full"
        >
          <MessageSquare className="h-4 w-4" />
          Open Inbox
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="rounded-full">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem>
              Export Data
            </DropdownMenuItem>
            <DropdownMenuItem>
              Move to Folder
            </DropdownMenuItem>
            <DropdownMenuItem className="text-red-600">
              <Flag className="h-4 w-4 mr-2" />
              Flag Deal
            </DropdownMenuItem>
            <DropdownMenuItem className="text-red-600">
              Archive Deal
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </motion.div>
  )
}
