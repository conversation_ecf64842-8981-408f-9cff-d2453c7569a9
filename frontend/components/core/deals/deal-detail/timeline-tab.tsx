"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Clock, 
  FileText, 
  Target, 
  User, 
  Settings,
  CheckCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, TimelineEvent } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface TimelineTabProps {
  deal: DealDetailData
}

const getEventIcon = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return Settings
    case 'user':
      return User
    case 'score':
      return Target
    case 'document':
      return FileText
    case 'status':
      return CheckCircle
    default:
      return Clock
  }
}

const getEventColor = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return 'bg-gray-100 text-gray-600 border-gray-200'
    case 'user':
      return 'bg-blue-100 text-blue-600 border-blue-200'
    case 'score':
      return 'bg-green-100 text-green-600 border-green-200'
    case 'document':
      return 'bg-purple-100 text-purple-600 border-purple-200'
    case 'status':
      return 'bg-orange-100 text-orange-600 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

const groupEventsByDate = (events: TimelineEvent[]) => {
  const groups: { [key: string]: TimelineEvent[] } = {}
  
  events.forEach(event => {
    const date = new Date(event.date).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(event)
  })
  
  return Object.entries(groups).sort(([a], [b]) => 
    new Date(b).getTime() - new Date(a).getTime()
  )
}

export function TimelineTab({ deal }: TimelineTabProps) {
  const events = deal.timeline || []
  
  if (events.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-16 border shadow-sm">
        <div className="text-center">
          <Clock className="mx-auto h-16 w-16 text-gray-400 mb-6" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-3">No timeline events yet</h2>
          <p className="text-gray-600 max-w-md mx-auto text-lg">
            Timeline events will appear here as actions are taken on this deal. Key milestones and activities will be tracked automatically.
          </p>
        </div>
      </div>
    )
  }

  const groupedEvents = groupEventsByDate(events)

  return (
    <div className="w-full space-y-12">
      {groupedEvents.map(([dateString, dayEvents], groupIndex) => (
        <motion.div
          key={dateString}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: groupIndex * 0.15 }}
          className="bg-white rounded-2xl border shadow-sm overflow-hidden"
        >
          {/* Date Header */}
          <div className="bg-gray-50/80 px-8 py-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {formatDate(dayEvents[0].date)}
            </h2>
            <p className="text-gray-600 text-sm mt-1">
              {dayEvents.length} {dayEvents.length === 1 ? 'event' : 'events'} recorded
            </p>
          </div>

          {/* Events for this date */}
          <div className="p-8">
            <div className="space-y-6">
              {dayEvents
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .map((event, eventIndex) => {
                  const Icon = getEventIcon(event.type)
                  
                  return (
                    <motion.div
                      key={event.id}
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: eventIndex * 0.1 }}
                      className="relative"
                    >
                      {/* Connecting line for multiple events */}
                      {eventIndex < dayEvents.length - 1 && (
                        <div className="absolute left-6 top-16 w-0.5 h-8 bg-gray-200" />
                      )}
                      
                      <div className="flex items-start gap-6">
                        {/* Event Icon */}
                        <div className={cn(
                          "flex items-center justify-center w-12 h-12 rounded-xl border-2 shadow-sm bg-white z-10",
                          getEventColor(event.type)
                        )}>
                          <Icon className="h-6 w-6" />
                        </div>

                        {/* Event Content */}
                        <div className="flex-1 min-w-0 bg-gray-50 rounded-xl p-6 border">
                          <div className="flex items-start justify-between gap-6">
                            <div className="flex-1">
                              <h3 className="font-semibold text-lg text-gray-900 mb-2">
                                {event.event}
                              </h3>
                              {event.description && (
                                <p className="text-gray-700 leading-relaxed mb-4">
                                  {event.description}
                                </p>
                              )}
                              
                              {/* Event Metadata */}
                              <div className="flex items-center gap-4">
                                {event.user_name && (
                                  <div className="flex items-center gap-2">
                                    <Avatar className="h-7 w-7">
                                      <AvatarFallback className="text-sm bg-primary text-white">
                                        {event.user_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span className="text-sm font-medium text-gray-700">
                                      {event.user_name}
                                    </span>
                                  </div>
                                )}
                                
                                <Badge 
                                  variant="secondary" 
                                  className="text-sm px-3 py-1 bg-white border"
                                >
                                  {event.type}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="text-right">
                              <div className="text-sm font-medium text-gray-900">
                                {formatTime(event.date)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}
