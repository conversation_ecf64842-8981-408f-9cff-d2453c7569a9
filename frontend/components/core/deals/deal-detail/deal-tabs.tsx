"use client"

import { motion } from "framer-motion"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import { 
  Clock, 
  Target, 
  Users, 
  Radio, 
  FileText, 
  BarChart3 
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  },
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'signals',
    label: 'External Signals',
    icon: Radio,
    component: SignalsTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  }
]

export function DealTabs({ deal, activeTab, onTabChange }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'signals':
        return deal.external_signals?.length || 0
      case 'documents':
        return deal.documents?.length || 0
      default:
        return null
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
      className="w-full"
    >
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        {/* Professional Tab Navigation */}
        <div className="w-full mb-12">
          <TabsList className="grid w-full grid-cols-6 h-auto p-2 bg-white rounded-xl border shadow-sm">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const count = getTabCount(tab.id)
              
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  disabled={tab.disabled}
                  className="flex flex-col items-center gap-2 py-6 px-8 data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:shadow-lg rounded-lg transition-all duration-300 font-medium text-gray-600 hover:text-gray-900 data-[state=active]:hover:text-white"
                >
                  <Icon className="h-6 w-6" />
                  <span className="text-sm font-medium">{tab.label}</span>
                  
                  {count !== null && count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="h-5 min-w-[24px] text-xs px-2 rounded-full bg-gray-100 text-gray-700 data-[state=active]:bg-white/20 data-[state=active]:text-white"
                    >
                      {count}
                    </Badge>
                  )}
                  
                  {tab.disabled && (
                    <Badge 
                      variant="outline" 
                      className="h-5 text-xs px-2 text-gray-400 border-gray-300 rounded-full"
                    >
                      Soon
                    </Badge>
                  )}
                </TabsTrigger>
              )
            })}
          </TabsList>
        </div>

        {/* Professional Tab Content */}
        <div className="w-full">
          {tabs.map((tab) => {
            const Component = tab.component
            
            return (
              <TabsContent 
                key={tab.id} 
                value={tab.id}
                className="mt-0 focus-visible:outline-none w-full"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full"
                >
                  <Component deal={deal} />
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </motion.div>
  )
}
