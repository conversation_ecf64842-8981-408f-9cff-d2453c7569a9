"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Target,
  Users,
  TrendingUp,
  CheckCircle,
  ExternalLink,
  ChevronRight,
  Zap,
  AlertTriangle,
  Info,
  Star
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, SignalScore } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ScoreTabProps {
  deal: DealDetailData
}

const getScoreGradient = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

const getInsightColor = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return 'text-green-600 bg-green-50 border-green-200'
    case 'negative': return 'text-red-600 bg-red-50 border-red-200'
    case 'neutral': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  }
}

const getInsightIcon = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return CheckCircle
    case 'negative': return AlertTriangle
    case 'neutral': return Info
  }
}

const signals = [
  {
    id: 'team_strength',
    title: 'Team Strength',
    icon: Users,
    description: 'Founder experience, team composition, and track record'
  },
  {
    id: 'market_signals',
    title: 'Market Signals',
    icon: TrendingUp,
    description: 'Market size, growth trends, and competitive landscape'
  },
  {
    id: 'thesis_match',
    title: 'Thesis Match',
    icon: Target,
    description: 'Alignment with investment criteria and strategic focus'
  }
]

export function ScoreTab({ deal }: ScoreTabProps) {
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)
  const [animatedScore, setAnimatedScore] = useState(0)
  const scoreBreakdown = deal.score_breakdown

  // Animate score on mount
  useEffect(() => {
    if (scoreBreakdown?.overall_score) {
      const timer = setTimeout(() => {
        setAnimatedScore(scoreBreakdown.overall_score)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [scoreBreakdown?.overall_score])

  const handleViewFullAnalysis = () => {
    // Navigate to full analysis page
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  if (!scoreBreakdown) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No scoring data available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been scored yet. Scoring will appear here once the analysis is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="space-y-8">
      {/* Overall Score Card - Magic Moment */}
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <Card className={cn(
          "relative overflow-hidden border-0 shadow-xl",
          getScoreBackground(scoreBreakdown.overall_score)
        )}>
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-primary" />
                  <h3 className="text-xl font-bold">Overall Score</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Last updated {new Date(scoreBreakdown.last_updated).toLocaleDateString()}
                </p>
                <p className="text-sm font-medium text-primary">
                  Powered by AI
                </p>
              </div>

              <div className="relative">
                {/* Animated Score Circle */}
                <div className={cn(
                  "flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br shadow-2xl",
                  getScoreGradient(scoreBreakdown.overall_score)
                )}>
                  <div className="flex items-center justify-center w-20 h-20 rounded-full bg-white dark:bg-gray-900">
                    <motion.span
                      className="text-3xl font-bold text-gray-900 dark:text-white"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 1, delay: 0.8 }}
                      >
                        {animatedScore}
                      </motion.span>
                    </motion.span>
                  </div>
                </div>

                {/* Subtle glow effect */}
                <div className={cn(
                  "absolute inset-0 rounded-full bg-gradient-to-br opacity-20 blur-xl",
                  getScoreGradient(scoreBreakdown.overall_score)
                )} />
              </div>
            </div>

            <div className="mt-6">
              <Progress
                value={animatedScore}
                className="h-2"
              />
            </div>

            {scoreBreakdown.ai_summary && (
              <div className="mt-4 p-4 bg-white/50 dark:bg-gray-900/50 rounded-lg border border-white/20">
                <p className="text-sm text-gray-700 dark:text-gray-300 italic">
                  "{scoreBreakdown.ai_summary}"
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Signal Snapshot - PRD Specification */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold">Signal Snapshot</h3>
          <Button
            onClick={handleViewFullAnalysis}
            className="gap-2 bg-primary hover:bg-primary/90"
          >
            View Full Analysis
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>

        {/* Always three cards - PRD Specification */}
        <div className="grid gap-4">
          {signals.map((signal, index) => {
            const signalData = scoreBreakdown.signal_breakdown[signal.id as keyof typeof scoreBreakdown.signal_breakdown]
            const Icon = signal.icon

            return (
              <motion.div
                key={signal.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.15 }}
              >
                <Card className="border-0 shadow-sm hover:shadow-md transition-all duration-300 bg-white dark:bg-gray-900">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        <div className={cn(
                          "flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br",
                          getScoreGradient(signalData.score)
                        )}>
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <h4 className="font-semibold text-base">{signal.title}</h4>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {signalData.ai_insights || signalData.explanation}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            {signalData.score}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleViewFullAnalysis}
                          className="text-muted-foreground hover:text-foreground"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Key Insights Section */}
      {scoreBreakdown.key_insights && scoreBreakdown.key_insights.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Star className="h-5 w-5 text-primary" />
                Key Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scoreBreakdown.key_insights.map((insight, index) => {
                  const Icon = getInsightIcon(insight.type)
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                      className="flex items-start gap-4"
                    >
                      <div className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full border",
                        getInsightColor(insight.type)
                      )}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium leading-relaxed">
                          {insight.message}
                        </p>
                        <div className="flex items-center gap-2">
                          <div className="text-xs text-muted-foreground">
                            Confidence: {Math.round(insight.confidence * 100)}%
                          </div>
                          <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary rounded-full transition-all duration-1000"
                              style={{ width: `${insight.confidence * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}


    </div>
  )
}
