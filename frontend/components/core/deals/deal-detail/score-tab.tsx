"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Target,
  Users,
  TrendingUp,
  CheckCircle,
  ExternalLink,
  ChevronRight,
  Zap,
  AlertTriangle,
  Info,
  Star
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, SignalScore } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ScoreTabProps {
  deal: DealDetailData
}

const getScoreGradient = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

const getInsightColor = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return 'text-green-600 bg-green-50 border-green-200'
    case 'negative': return 'text-red-600 bg-red-50 border-red-200'
    case 'neutral': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  }
}

const getInsightIcon = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return CheckCircle
    case 'negative': return AlertTriangle
    case 'neutral': return Info
  }
}

const signals = [
  {
    id: 'team_strength',
    title: 'Team Strength',
    icon: Users,
    description: 'Founder experience, team composition, and track record'
  },
  {
    id: 'market_signals',
    title: 'Market Signals',
    icon: TrendingUp,
    description: 'Market size, growth trends, and competitive landscape'
  },
  {
    id: 'thesis_match',
    title: 'Thesis Match',
    icon: Target,
    description: 'Alignment with investment criteria and strategic focus'
  }
]

export function ScoreTab({ deal }: ScoreTabProps) {
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)
  const [animatedScore, setAnimatedScore] = useState(0)
  const scoreBreakdown = deal.score_breakdown

  // Animate score on mount
  useEffect(() => {
    if (scoreBreakdown?.overall_score) {
      const timer = setTimeout(() => {
        setAnimatedScore(scoreBreakdown.overall_score)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [scoreBreakdown?.overall_score])

  const handleViewFullAnalysis = () => {
    // Navigate to full analysis page
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  if (!scoreBreakdown) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No scoring data available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been scored yet. Scoring will appear here once the analysis is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="w-full space-y-12">
      {/* Hero Score Section - Ultra Professional */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="bg-white rounded-2xl shadow-sm border p-12"
      >
        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Score Details */}
            <div className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Zap className="h-7 w-7 text-primary" />
                  <h2 className="text-3xl font-bold text-gray-900">Overall Score</h2>
                </div>
                <p className="text-lg text-gray-600">
                  Last updated {new Date(scoreBreakdown.last_updated).toLocaleDateString()}
                </p>
              </div>

              {scoreBreakdown.ai_summary && (
                <div className="bg-gray-50 rounded-xl p-6 border">
                  <p className="text-gray-700 italic text-lg leading-relaxed">
                    "{scoreBreakdown.ai_summary}"
                  </p>
                </div>
              )}

              <div className="space-y-3">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <motion.div
                    className={cn(
                      "h-3 rounded-full bg-gradient-to-r",
                      getScoreGradient(scoreBreakdown.overall_score)
                    )}
                    initial={{ width: 0 }}
                    animate={{ width: `${animatedScore}%` }}
                    transition={{ duration: 1.5, delay: 0.5 }}
                  />
                </div>
                <p className="text-sm font-medium text-gray-500">
                  Powered by AI Analysis
                </p>
              </div>
            </div>

            {/* Right Side - Animated Score Circle */}
            <div className="flex justify-center">
              <div className="relative">
                <div className={cn(
                  "flex items-center justify-center w-48 h-48 rounded-full bg-gradient-to-br shadow-xl",
                  getScoreGradient(scoreBreakdown.overall_score)
                )}>
                  <div className="flex items-center justify-center w-40 h-40 rounded-full bg-white">
                    <motion.span
                      className="text-6xl font-bold text-gray-900"
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8, delay: 0.3 }}
                    >
                      {animatedScore}
                    </motion.span>
                  </div>
                </div>
                <div className={cn(
                  "absolute inset-0 rounded-full bg-gradient-to-br opacity-20 blur-2xl",
                  getScoreGradient(scoreBreakdown.overall_score)
                )} />
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Signal Analysis - Professional Grid */}
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-gray-900">Signal Analysis</h2>
            <p className="text-gray-600">Comprehensive evaluation across key investment criteria</p>
          </div>
          <Button
            onClick={handleViewFullAnalysis}
            size="lg"
            className="gap-3 px-8 py-3 text-base"
          >
            View Full Analysis
            <ExternalLink className="h-5 w-5" />
          </Button>
        </div>

        {/* Three-Column Professional Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {signals.map((signal, index) => {
            const signalData = scoreBreakdown.signal_breakdown[signal.id as keyof typeof scoreBreakdown.signal_breakdown]
            const Icon = signal.icon

            return (
              <motion.div
                key={signal.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <Card className="bg-white border shadow-sm hover:shadow-lg transition-all duration-300 h-full">
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-4">
                          <div className={cn(
                            "flex items-center justify-center w-14 h-14 rounded-xl bg-gradient-to-br shadow-lg",
                            getScoreGradient(signalData.score)
                          )}>
                            <Icon className="h-7 w-7 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900">{signal.title}</h3>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-3xl font-bold text-gray-900">
                            {signalData.score}
                          </div>
                          <div className="text-sm text-gray-500">/ 100</div>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-gray-600 leading-relaxed">
                        {signalData.ai_insights || signalData.explanation}
                      </p>

                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div
                            className={cn(
                              "h-2 rounded-full bg-gradient-to-r",
                              getScoreGradient(signalData.score)
                            )}
                            initial={{ width: 0 }}
                            animate={{ width: `${signalData.score}%` }}
                            transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                          />
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleViewFullAnalysis}
                          className="w-full text-gray-600 hover:text-gray-900"
                        >
                          View Details
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Key Insights Section */}
      {scoreBreakdown.key_insights && scoreBreakdown.key_insights.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
        >
          <Card className="border-0 shadow-md">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Star className="h-5 w-5 text-primary" />
                Key Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scoreBreakdown.key_insights.map((insight, index) => {
                  const Icon = getInsightIcon(insight.type)
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                      className="flex items-start gap-4"
                    >
                      <div className={cn(
                        "flex items-center justify-center w-8 h-8 rounded-full border",
                        getInsightColor(insight.type)
                      )}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium leading-relaxed">
                          {insight.message}
                        </p>
                        <div className="flex items-center gap-2">
                          <div className="text-xs text-muted-foreground">
                            Confidence: {Math.round(insight.confidence * 100)}%
                          </div>
                          <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary rounded-full transition-all duration-1000"
                              style={{ width: `${insight.confidence * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}


    </div>
  )
}
