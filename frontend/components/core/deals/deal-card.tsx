"use client"

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Deal, DealStatus } from '@/lib/types/deal';
import { MoreHorizontal, ExternalLink, MapPin, Flag, Archive, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
}

// Backend-aligned utility functions
const getStatusColor = (status: DealStatus) => {
  switch (status) {
    case DealStatus.NEW:
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case DealStatus.TRIAGE:
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case DealStatus.REVIEWED:
      return 'bg-purple-50 text-purple-700 border-purple-200';
    case DealStatus.APPROVED:
      return 'bg-green-50 text-green-700 border-green-200';
    case DealStatus.NEGOTIATING:
      return 'bg-orange-50 text-orange-700 border-orange-200';
    case DealStatus.CLOSED:
      return 'bg-gray-50 text-gray-700 border-gray-200';
    case DealStatus.EXCLUDED:
    case DealStatus.REJECTED:
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

const getScoreColor = (score: number) => {
  if (score >= 80) return 'bg-green-50 text-green-700 border-green-200';
  if (score >= 50) return 'bg-blue-50 text-blue-700 border-blue-200';
  return 'bg-yellow-50 text-yellow-700 border-yellow-200';
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500 text-white',
    'bg-green-500 text-white',
    'bg-purple-500 text-white',
    'bg-orange-500 text-white',
    'bg-pink-500 text-white',
    'bg-indigo-500 text-white',
    'bg-teal-500 text-white',
    'bg-red-500 text-white',
  ];

  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

const formatSector = (sector: string | string[] | undefined): string[] => {
  if (!sector) return [];
  if (Array.isArray(sector)) return sector;
  return [sector];
};

const createSummary = (deal: Deal): string => {
  const sectors = formatSector(deal.sector);
  const parts = [];

  if (sectors.length > 0) {
    parts.push(`${sectors[0]} company`);
  }

  if (deal.stage) {
    parts.push(`at ${deal.stage} stage`);
  }

  if (parts.length === 0) {
    return 'Investment opportunity in review';
  }

  return parts.join(' ') + '.';
};

export function DealCard({ deal, index, onClick }: DealCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        delay: index * 0.05
      }
    }
  };

  // Extract backend data with proper fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = formatSector(deal.sector);
  const stage = deal.stage;
  const website = deal.company_website;
  const score = deal.scoring?.total_score;
  const status = deal.status;
  const tags = deal.tags || [];

  // Generate company initials for avatar
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  const avatarColor = getAvatarColor(companyName);
  const summary = createSummary(deal);

  // Extract country from enriched_data or tags (placeholder logic)
  const country = deal.enriched_data?.country ||
                 tags.find(tag => tag.includes('Country:'))?.replace('Country:', '') ||
                 null;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group"
    >
      <Card className="relative overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300 bg-white rounded-xl cursor-pointer hover:-translate-y-1">
        <Link href={`/deals/${deal.id}`} className="block">
          <CardContent className="p-6">
            {/* Header Row */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                {/* Avatar */}
                <Avatar className="h-12 w-12 flex-shrink-0">
                  <AvatarFallback className={cn("text-sm font-semibold", avatarColor)}>
                    {initials}
                  </AvatarFallback>
                </Avatar>

                {/* Company Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-bold text-gray-900 leading-tight mb-1 truncate">
                    {companyName}
                  </h3>

                  {/* Status and Stage Chips */}
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={cn("text-xs font-medium rounded-full px-2 py-1", getStatusColor(status))}
                    >
                      {status.toUpperCase()}
                    </Badge>
                    {stage && (
                      <Badge
                        variant="outline"
                        className="text-xs font-medium bg-gray-50 text-gray-700 border-gray-200 rounded-full px-2 py-1"
                      >
                        {stage.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions Menu - Show on hover */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
                    onClick={(e) => e.preventDefault()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Flag className="h-4 w-4 mr-2" />
                    Flag Deal
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Archive className="h-4 w-4 mr-2" />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-red-600">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Sector Tags */}
            {sectors.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {sectors.slice(0, 2).map((sector, idx) => (
                  <Badge
                    key={idx}
                    variant="outline"
                    className="text-xs font-medium bg-gray-50 text-gray-700 border-gray-200 rounded-full px-2 py-1"
                  >
                    {sector.toUpperCase()}
                  </Badge>
                ))}
              </div>
            )}

            {/* Summary */}
            <p className="text-sm text-gray-600 leading-relaxed mb-4 line-clamp-2">
              {summary}
            </p>

            {/* Bottom Row - Score, Website, Country */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {score && (
                  <Badge
                    variant="outline"
                    className={cn("text-xs font-semibold rounded-full px-2 py-1", getScoreColor(score))}
                  >
                    {score}
                  </Badge>
                )}
                {website && (
                  <Badge
                    variant="outline"
                    className="text-xs text-gray-600 bg-gray-50 border-gray-200 rounded-full px-2 py-1 flex items-center gap-1 cursor-pointer hover:bg-gray-100"
                    onClick={(e) => {
                      e.preventDefault();
                      window.open(website.startsWith('http') ? website : `https://${website}`, '_blank');
                    }}
                  >
                    <ExternalLink className="h-3 w-3" />
                    Website
                  </Badge>
                )}
              </div>

              {/* Country */}
              <div className="text-xs text-gray-500 font-medium">
                {country || '—'}
              </div>
            </div>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  );
}
